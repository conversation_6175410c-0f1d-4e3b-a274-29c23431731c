import 'dart:io';

import 'package:core/store.dart';
import 'package:drift/configuration.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

import 'migrations.dart';

class SqliteStore extends Store {
  Database? _instance;

  @override
  Future<void> initialize() async {
    if (_instance != null) return;

    // Initialize sqflite for desktop platforms (Windows, Linux, macOS)
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    try {
      _instance = await openDatabase(
        await location,
        version: Configuration.databaseVersion,
        onCreate: (database, version) async {
          await applyMigrations(database, 0, version);
        },
        onUpgrade: (database, oldVersion, newVersion) async {
          await applyMigrations(database, oldVersion, newVersion);
        },
      );
    } catch (error) {
      rethrow;
    }
  }

  @override
  Future<List<Map<String, Object?>>> query(
    String table, {
    Map<String, String>? where,
    int? timestamp = 0,
  }) async {
    if (_instance == null) throw StateError('Database not initialized');

    if (where != null) {
      return _instance!.query(
        table,
        where: where.entries.map((e) => '${e.key} = ?').join(' AND '),
        whereArgs: where.values.toList(),
        orderBy: 'created_at ASC',
      );
    }

    return _instance!.query(
      table,
      where: 'created_at > ?',
      whereArgs: [timestamp],
      orderBy: 'created_at ASC',
    );
  }

  @override
  Future<void> insert(String table, Map<String, Object?> values) {
    if (_instance == null) throw StateError('Database not initialized');

    return _instance!.insert(
      table,
      values,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  @override
  Future<void> insertAll(
    String table,
    List<Map<String, Object?>> listOfValues,
  ) {
    if (_instance == null) throw StateError('Database not initialized');

    return _instance!.transaction((txn) async {
      for (final values in listOfValues) {
        await txn.insert(
          table,
          values,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  @override
  Future<String> get location async {
    final directory = await getApplicationDocumentsDirectory();
    return path.join(directory.path, Configuration.databaseName);
  }

  @override
  Future<void> dispose() async {
    await _instance?.close();
    _instance = null;
  }

  @override
  Future<void> clear() async {
    await dispose();

    final file = File(await location);

    if (await file.exists()) {
      await file.delete();
    }

    await initialize();
  }
}

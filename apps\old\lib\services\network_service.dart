import 'dart:convert';

import 'package:core/core.dart';
import 'package:core/services.dart';
import 'package:drift/shared/tailscale/client.dart';
import 'package:http/http.dart';

class LocalNetworkService extends NetworkService {
  TailscaleClient tailscale;

  LocalNetworkService({required this.tailscale});

  @override
  Future<List<Node>> getNodes() async {
    return await tailscale.getDevices();
  }

  @override
  Future<List<Event>> pull(Node node, {DateTime? since}) async {
    throw UnimplementedError();
  }

  @override
  Future<void> push(Node node, List<Event> events) async {
    final host = node.alias;
    final url = Uri.parse('http://$host:4547/api/push');
    final body = jsonEncode(events.map((e) => e.toJson()).toList());

    await post(url, body: body);
  }
}

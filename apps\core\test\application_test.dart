import 'package:core/application.dart';
import 'package:core/services.dart';
import 'package:core/src/models/event.dart';
import 'package:core/src/node.dart';
import 'package:test/test.dart';

class Mock extends NetworkService {
  @override
  Future<List<Node>> getNodes() async {
    return [];
  }

  @override
  Future<List<Event>> pull(Node node, {DateTime? since}) {
    throw UnimplementedError();
  }

  @override
  Future<void> push(Node node, List<Event> events) {
    throw UnimplementedError();
  }
}

void main() {
  group('NetworkManager', () {
    test('should get nodes', () async {
      final manager = NetworkManager(network: Mock());
      final nodes = await manager.getNodes();
      var string = '  foo ';
      print(string);
      expect(string.trim(), equals('foo'));
    });
  });
}

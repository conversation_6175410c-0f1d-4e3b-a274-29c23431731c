abstract class Store {
  Future<void> initialize();
  Future<void> dispose();
  Future<void> clear();

  Future<List<Map<String, Object?>>> query(
    String table, {
    Map<String, String>? where,
    int? timestamp,
  });

  Future<void> insert(String table, Map<String, Object?> values);
  Future<void> insertAll(String table, List<Map<String, Object?>> listOfValues);

  Future<String> get location;
}

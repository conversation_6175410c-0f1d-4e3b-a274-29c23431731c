import 'dart:async';
import 'dart:io';

import 'package:core/core.dart';
import 'package:flutter/foundation.dart';
import 'package:system_tray/system_tray.dart';
import 'package:window_manager/window_manager.dart';

const icon = 'lib/assets/logo.ico';

class BackgroundService {
  static SystemTray? _systemTray;
  static bool _initialized = false;

  static Future<void> initialize(Core driftCore) async {
    if (_initialized) return;

    if (Platform.isAndroid) {
    } else if (Platform.isWindows || Platform.isLinux) {
      try {
        _systemTray = SystemTray();
        await _systemTray!.initSystemTray(title: 'Drift', iconPath: icon);
        await _systemTray!.setContextMenu([
          MenuItem(label: 'Show Drift', onClicked: () => _showWindow()),
          MenuItem(label: 'Exit', onClicked: () => exit(0)),
        ]);
        _systemTray!.registerSystemTrayEventHandler((eventName) {
          if (eventName == 'rightMouseUp') {
            _systemTray!.popUpContextMenu();
          } else if (eventName == 'leftMouseUp') {
            _showWindow();
          }
        });
      } catch (e) {
        if (kDebugMode) print('System tray failed: $e');
        _systemTray = null;
      }
    }

    _initialized = true;
  }

  static Future<void> startService() async {
    if (Platform.isAndroid) {}
  }

  static void dispose() {
    _systemTray = null;
    _initialized = false;
  }

  static void _showWindow() async {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      await windowManager.show();
      await windowManager.focus();
    }
  }
}

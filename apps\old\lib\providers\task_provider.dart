import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:core/core.dart';

import '../models/task.dart';

/// Provider for managing task state in Flutter
class TaskProvider extends ChangeNotifier {
  final Core _core;
  final List<TaskModel> _tasks = [];
  bool _isLoading = false;
  String? _error;
  StreamSubscription<String>? _dataChangeSubscription;

  TaskProvider(this._core) {
    _initialize();
  }

  @override
  void dispose() {
    _dataChangeSubscription?.cancel();
    super.dispose();
  }

  /// Get the list of tasks
  List<TaskModel> get tasks => List.unmodifiable(_tasks);

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Get node ID
  String get nodeId => _core.nodeId;

  /// Get known nodes count
  int get knownNodesCount => 0;

  /// Debug method to print sync status
  void printSyncStatus() {}

  /// Initialize the provider
  Future<void> _initialize() async {
    await _loadTasks();
  }

  /// Load tasks from the database
  Future<void> _loadTasks() async {
    _setLoading(true);
    _setError(null);
  }

  /// Create a new task
  Future<void> createTask(String title, String description) async {
    _setLoading(true);
    _setError(null);
  }

  /// Complete a task
  Future<void> completeTask(String taskId) async {
    _setLoading(true);
    _setError(null);
  }

  /// Refresh tasks and sync (pull from all connected devices)
  Future<void> refresh() async {
    _setLoading(true);
    _setError(null);
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
}

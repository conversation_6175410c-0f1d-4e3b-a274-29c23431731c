import 'dart:io';

import 'package:core/core.dart';
import 'package:drift/shared/shelf/server.dart';
import 'package:drift/shared/sqlite/store.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';

import 'main.dart';

/// This entry point should be used when running the app in debug mode.
/// It sets up debug-specific configurations before running the app.

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (Platform.isAndroid) {
    await initializeService();
  }

  final driftCore = Core();
  await driftCore.initialize();

  runApp(RunnerApp(driftCore: driftCore));
}

Future<void> initializeService() async {
  await FlutterBackgroundService().configure(
    androidConfiguration: AndroidConfiguration(
      onStart: onStart,
      autoStart: true,
      isForegroundMode: true,
      autoStartOnBoot: false,
    ),
    iosConfiguration: IosConfiguration(),
  );
  await FlutterBackgroundService().startService();
}

@pragma('vm:entry-point')
void onStart(ServiceInstance service) async {
  final server = Server(eventStore: SqliteStore());
  await server.start();
}

/// A wrapper widget that provides debug-specific features
class RunnerApp extends StatelessWidget {
  final Core driftCore;

  const RunnerApp({super.key, required this.driftCore});

  @override
  Widget build(BuildContext context) {
    return DriftApp(driftCore: driftCore);
  }
}

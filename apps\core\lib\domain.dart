import 'package:uuid/uuid.dart';

class Project {
  final UniqueId id;
  final String title;
  final String description;
  final String content;

  Project({
    UniqueId? id,
    required this.title,
    required this.description,
    required this.content,
  }) : id = id ?? UniqueId.create();
}

class Note {
  final UniqueId id;
  final String title;
  final String description;
  final String content;
  final List<Reference> references;

  Note({
    UniqueId? id,
    required this.title,
    required this.description,
    required this.content,
    required this.references,
  }) : id = id ?? UniqueId.create();
}

class Entry {
  final UniqueId id;
  final String content;
  final DateTime createdAt;
  final List<Reference> references;

  Entry({
    UniqueId? id,
    required this.content,
    required this.references,
    DateTime? createdAt,
  })  : id = id ?? UniqueId.create(),
        createdAt = createdAt ?? DateTime.now().toUtc();
}

class Reference<T> {
  final T entity;
  final String? source;

  Reference({
    required this.entity,
    this.source,
  });
}

enum EntityType {
  task,
  note,
  reminder,
  event,
  tracker,
}

enum EventType {
  create,
  update,
  delete,
  sync,
}

class UniqueId {
  final String value;

  UniqueId(this.value);

  static UniqueId create() => UniqueId(Uuid().v4());
  static UniqueId get() => UniqueId('00000000-0000-0000-0000-000000000000');

  @override
  bool operator ==(Object other) => other is UniqueId && other.value == value;

  @override
  int get hashCode => value.hashCode;
}

class Event {
  final UniqueId id;
  final EventType type;
  final EntityType entity; // entity id
  final UniqueId origin; // node id
  final DateTime createdAt; // time in UTC

  final List<Record> records = [];

  Event.create({
    UniqueId? id,
    this.type = EventType.create,
    this.entity = EntityType.task,
    UniqueId? origin,
    DateTime? createdAt,
  })  : id = UniqueId.create(),
        origin = UniqueId.get(),
        createdAt = DateTime.now().toUtc();
}

class Record {
  final UniqueId id;
  final Field property;
  final String value;
  final DateTime createdAt; // time in UTC

  Record({
    UniqueId? id,
    required this.property,
    required this.value,
    DateTime? createdAt,
  })  : id = UniqueId.create(),
        createdAt = DateTime.now().toUtc();
}

enum FieldType { text, number, date, boolean }

class Field {
  final String key;
  final FieldType type;
  final bool required;

  Field({
    required this.key,
    this.required = false,
  }) : type = FieldType.text;

  Field.number({
    required this.key,
    this.required = false,
  }) : type = FieldType.number;

  Field.date({
    required this.key,
    this.required = false,
  }) : type = FieldType.date;

  Field.boolean({
    required this.key,
    this.required = false,
  }) : type = FieldType.boolean;
}

enum Fields {
  title,
  subtitle,
  description,
  body,
  url,
  dealine,
  progress, // from 0 to 100
  priority,
  status,
  author,
  createdAt,
  updatedAt,
  deletedAt,
}

extension FieldsToString on Fields {
  String get value {
    final regex = RegExp(r'([A-Z])');
    final replace = (match) => '_${match[1]?.toLowerCase()}';
    return name.replaceAllMapped(regex, replace).toLowerCase();
  }
}

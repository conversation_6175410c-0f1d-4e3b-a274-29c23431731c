import 'package:flutter/services.dart';
import 'package:sqflite/sqflite.dart';

final Map<int, String> _migrations = {
  1: '001_initial_schema.sql',
  // 2: '002_update_schema.sql',
};

Future<void> applyMigrations(Database database, int from, int to) async {
  if (from >= to) {
    return;
  }

  for (int version = from; version <= to; version++) {
    final file = _migrations[version];

    if (file == null) {
      continue;
    }

    try {
      final path = 'lib/shared/sqlite/migrations/$file';
      final sql = await rootBundle.loadString(path);
      final statements = sql.split(';').map((s) => s.trim()).toList();

      for (final statement in statements) {
        if (statement.trim().isNotEmpty) {
          await database.execute(statement);
        }
      }
    } catch (e) {
      rethrow;
    }
  }
}
